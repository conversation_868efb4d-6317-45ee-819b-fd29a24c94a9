import { createRouter, createWebHashHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import LinuxLogin from '../views/LinuxLogin.vue'  // Added Linux login component
import Email from '../views/Email.vue'
import EmailDetail from '../views/EmailDetail.vue'
import ComposeEmail from '../views/ComposeEmail.vue'
import Schedule from '../views/Schedule.vue'
import AddSchedule from '../views/AddSchedule.vue'
import Documents from '../views/Documents.vue'
import DocumentDetail from '../views/DocumentDetail.vue'
import Contacts from '../views/Contacts.vue'
import ContactDetail from '../views/ContactDetail.vue'
import Profile from '../views/Profile.vue'
import Meeting from '../views/Meeting.vue'
import MeetingBookingForm from '../views/MeetingBookingForm.vue'
import MeetingApplication from '../views/MeetingApplication.vue'
import MeetingRoomBooking from '../views/MeetingRoomBooking.vue'
import Detail from '../views/Detail.vue'
import AboutApp from '../views/AboutApp.vue'
import SecuritySettings from '../views/SecuritySettings.vue'
import ChangePassword from '../views/ChangePassword.vue'
import Transfer from '../views/Transfer.vue'
import WebView from '../views/WebView.vue'

const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', name: 'Login', component: Login },
  { path: '/linux-login', name: 'LinuxLogin', component: LinuxLogin },  // Added Linux login route
  { path: '/home', name: 'Home', component: Home },
  { path: '/email', name: 'Email', component: Email },
  { path: '/email/detail/:id', name: 'EmailDetail', component: EmailDetail, props: true },
  { path: '/email/compose', name: 'ComposeEmail', component: ComposeEmail },
  { path: '/email/compose/:id', name: 'ComposeEmailReply', component: ComposeEmail, props: true },
  { path: '/schedule', name: 'Schedule', component: Schedule },
  { path: '/schedule/add', name: 'AddSchedule', component: AddSchedule },
  { path: '/documents', name: 'Documents', component: Documents },
  { path: '/documents/:id', name: 'DocumentDetail', component: DocumentDetail, props: true },
  { path: '/contacts', name: 'Contacts', component: Contacts },
  { path: '/contacts/:id', name: 'ContactDetail', component: ContactDetail, props: true },
  { path: '/profile', name: 'Profile', component: Profile },
  { path: '/meeting', name: 'Meeting', component: Meeting },
  { path: '/meeting/booking', name: 'MeetingBookingForm', component: MeetingBookingForm },
  { path: '/meeting/application', name: 'MeetingApplication', component: MeetingApplication },
  { path: '/meeting/room-booking', name: 'MeetingRoomBooking', component: MeetingRoomBooking },
  { path: '/detail/:id', name: 'Detail', component: Detail, props: true },
  { path: '/about', name: 'AboutApp', component: AboutApp },
  { path: '/security', name: 'SecuritySettings', component: SecuritySettings },
  { path: '/change-password', name: 'ChangePassword', component: ChangePassword },
  { path: '/transfer', name: 'Transfer', component: Transfer },
  { path: '/webview', name: 'WebView', component: WebView }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
