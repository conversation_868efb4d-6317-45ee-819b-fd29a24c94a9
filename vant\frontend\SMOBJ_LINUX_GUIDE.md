# SMObj Control Usage Guide for Linux

## Overview

This document describes how to use the SMObj control for digital certificate authentication on Linux systems. The implementation supports major Linux distributions and browsers including Firefox and Chrome.

## Supported Environments

1. **Operating Systems**:
   - Ubuntu 18.04+
   - CentOS 7+
   - Red Hat Enterprise Linux 7+
   - Other mainstream Linux distributions

2. **Browsers**:
   - Firefox 52+
   - Google Chrome 64+
   - Other Chromium-based browsers

## Installation Requirements

### 1. Browser Plugin Installation

For Linux systems to work with the SMObj control, you need to install the appropriate plugin:

1. Download the Linux plugin package from your certificate authority
2. Extract the package to a temporary directory
3. Install the plugin according to your browser:

#### Firefox Installation:
```bash
# Create plugins directory if it doesn't exist
mkdir -p ~/.mozilla/plugins

# Copy the plugin file
cp libsignature_manager.so ~/.mozilla/plugins/

# Restart Firefox
```

#### Chrome/Chromium Installation:
```bash
# Create plugins directory
mkdir -p ~/.config/google-chrome/Plugins

# Copy the plugin file
cp libsignature_manager.so ~/.config/google-chrome/Plugins/

# Restart Chrome
```

### 2. USB Key Driver Installation

Install the appropriate driver for your USB Key:

```bash
# Example for a generic USB Key driver
sudo dpkg -i usbkey-driver-linux.deb

# Or for RPM-based systems
sudo rpm -ivh usbkey-driver-linux.rpm

# Load the driver
sudo modprobe usbkey
```

## Technical Implementation

### Object Creation

The SMObj control is created differently depending on the environment:

```javascript
// For Linux systems
document.write('<object id="SMObj" width="0" height="0" type="application/signature_manager"></object>');
```

### Platform Detection

The system automatically detects Linux platforms:

```javascript
function isLinux() {
    return navigator.platform.indexOf('Linux') !== -1;
}
```

### Key Functions Available on Linux

Most functions from the Windows version are available with the same interface:

1. `WebGetKeyCount()` - Get number of connected keys
2. `WebOpenKey(index)` - Open a key handle
3. `WebGetKeySerialNumber(handle)` - Get key serial number
4. `WebKeyIsAdmin(handle)` - Check if key is admin key
5. `WebVerifyPin(handle, password)` - Verify key PIN
6. `WebGetCertData(handle, index)` - Get certificate data
7. `WebSign(handle, data)` - Sign data with key

### Linux-Specific Considerations

1. **Return Values**: Some functions return different values on Linux:
   ```javascript
   // On Windows IE, success = 0
   // On Linux, success = 1
   var result = SMObj.WebVerifyPin(keyHandle, password);
   if (!isIE()) {
       if (result == 1) {
           result = 0; // Normalize to Windows behavior
       } else {
           result = 1;
       }
   }
   ```

2. **Error Handling**: Linux may have different error messages:
   ```javascript
   try {
       var result = SMObj.WebFunction();
       // Handle result
   } catch (e) {
       console.error("Linux SMObj Error: " + e.message);
       // Show user-friendly error message
   }
   ```

## Usage Examples

### Basic Key Detection

```javascript
function checkForKey() {
    try {
        const SMObj = getSMObj();
        if (!SMObj) {
            throw new Error("SMObj not available");
        }
        
        const keyCount = SMObj.WebGetKeyCount();
        if (keyCount === 0) {
            return { success: false, message: "请插入USB Key" };
        }
        
        const keyHandle = SMObj.WebOpenKey(0);
        const keySN = SMObj.WebGetKeySerialNumber(keyHandle);
        SMObj.WebCloseKey(keyHandle);
        
        return { success: true, serialNumber: keySN };
    } catch (error) {
        return { success: false, message: "无法检测USB Key: " + error.message };
    }
}
```

### User Authentication

```javascript
async function authenticateUser(pin) {
    try {
        const SMObj = getSMObj();
        const keyCount = SMObj.WebGetKeyCount();
        
        if (keyCount === 0) {
            throw new Error("未检测到USB Key");
        }
        
        if (keyCount > 1) {
            throw new Error("请只插入一个USB Key");
        }
        
        const keyHandle = SMObj.WebOpenKey(0);
        let result = SMObj.WebVerifyPin(keyHandle, pin);
        
        // Normalize result for non-IE browsers
        if (!isIE()) {
            result = result == 1 ? 0 : 1;
        }
        
        SMObj.WebCloseKey(keyHandle);
        
        if (result !== 0) {
            throw new Error("PIN码错误");
        }
        
        return { success: true };
    } catch (error) {
        return { success: false, message: error.message };
    }
}
```

## Troubleshooting

### Common Issues

1. **Plugin Not Detected**:
   - Check if the plugin is installed in the correct directory
   - Verify browser supports NPAPI plugins
   - Restart the browser after installation

2. **USB Key Not Recognized**:
   - Check if the USB Key driver is properly installed
   - Verify the USB Key is properly inserted
   - Check system logs: `dmesg | grep -i usb`

3. **Permission Issues**:
   ```bash
   # Add user to appropriate groups
   sudo usermod -a -G dialout $USER
   
   # Set proper permissions for USB devices
   sudo chmod 666 /dev/bus/usb/*/*
   ```

4. **Firefox Plugin Issues**:
   - Check `about:plugins` in Firefox address bar
   - Ensure plugins are enabled
   - Try disabling tracking protection for the site

### Debugging Steps

1. **Check Browser Console**:
   Open Developer Tools (F12) and check for any JavaScript errors.

2. **Verify Plugin Installation**:
   ```javascript
   // Test if plugin is available
   if (navigator.mimeTypes && navigator.mimeTypes.length > 0) {
       const mimeTypes = navigator.mimeTypes;
       for (let i = 0; i < mimeTypes.length; i++) {
           if (mimeTypes[i].type === 'application/signature_manager') {
               console.log('SMObj plugin found');
               break;
           }
       }
   }
   ```

3. **Test Object Creation**:
   ```javascript
   const SMObj = getSMObj();
   if (SMObj) {
       console.log('SMObj created successfully');
   } else {
       console.error('Failed to create SMObj');
   }
   ```

## Security Considerations

1. **Always use HTTPS** for production environments
2. **Validate all inputs** from the SMObj control
3. **Handle errors gracefully** without exposing system information
4. **Implement proper session management** after authentication
5. **Regularly update** USB Key drivers and browser plugins

## Browser Compatibility Notes

### Firefox
- Supports NPAPI plugins up to version 52 ESR
- Later versions require WebExtensions or enterprise policy

### Chrome
- NPAPI support removed after version 45
- Requires enterprise policy or alternative implementation

### Alternative Implementation
For modern browsers that don't support NPAPI, consider:
1. WebSocket connection to a local service
2. Native messaging with browser extensions
3. Electron-based wrapper application

## Support

For technical support, contact your system administrator or the certificate authority that provided your USB Keys and plugins.