import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { Button, Cell, CellGroup, Icon, Tabbar, TabbarItem, Form, Field, Notify, Loading, NavBar, Toast, List, PullRefresh, Tabs, Tab, Swipe, SwipeItem, Lazyload, Popup, Picker, DatetimePicker, Uploader, ActionSheet, Dialog, Search, Divider, Tag, Sticky, Overlay } from 'vant';
import '@vant/touch-emulator';
import './utils/smobj.js'; // Import SMObj utility

// 全局引入vant组件
const app = createApp(App);

app.use(Button)
app.use(Cell)
app.use(CellGroup)
app.use(Icon)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Form)
app.use(Field)
app.use(Notify)
app.use(Loading)
app.use(NavBar)
app.use(Toast)
app.use(List)
app.use(PullRefresh)
app.use(Tabs)
app.use(Tab)
app.use(Swipe)
app.use(SwipeItem)
app.use(Lazyload)
app.use(Popup)
app.use(Picker)
app.use(DatetimePicker)
app.use(Uploader)
app.use(ActionSheet)
app.use(Dialog)
app.use(Search)
app.use(Divider)
app.use(Tag)
app.use(Sticky)
app.use(Overlay)

app.use(router)

app.mount('#app')