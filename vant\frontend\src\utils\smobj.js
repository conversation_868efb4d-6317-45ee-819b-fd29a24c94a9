/**
 * <PERSON>Obj Utility for Cross-platform Support
 * Supports Windows IE, Windows Chrome/Firefox, Linux and other信创 environments
 */

function isIE() {
    if(!!window.ActiveXObject || "ActiveXObject" in window){
        return true;
    } else {
        return false;
    }
}

function isLinux() {
    return navigator.platform.indexOf('Linux') !== -1;
}

// Create the appropriate SMObj based on platform
function createSMObj() {
    if (isIE()) {
        // Windows IE environment
        document.write('<object id="SMObj" style="display: none;" width="0" height="0" classid="clsid:014A514F-A762-4869-BFFE-7999CDFEC055" VIEWASTEXT></object>');
    } else if (isLinux()) {
        // Linux environment - using plugin approach
        // For Linux, we typically use the same plugin approach as 信创环境
        document.write('<object id="SMObj" width="0" height="0" type="application/signature_manager"></object>');
    } else {
        // 信创环境下Firefox、360 etc.
        document.write('<object id="SMObj" width="0" height="0" type="application/signature_manager"></object>');
    }
    
    // Hardware extension object (common for all platforms)
    document.write('<object id="KG_HARD_EXT" width="0" style="display: none;" height="0" classid="CLSID:7848343F-1875-4F22-9E0C-96BC8BF9796C" codebase="KG_HARD_EXT.ocx"></object>');
}

// Initialize the objects
createSMObj();

// Client path and parameters
var client_path = window['clientUtil'] ? window['clientUtil'].src : window.location.search;
if (client_path) {
    client_path = client_path.substring(client_path.indexOf("?") + 1);
    var client_params = client_path.split("&");
    var needAdminKey;
    client_params.forEach(function(v){  
        if(v.indexOf("needAdminKey=") > -1){
            needAdminKey = v.split("=")[1];
        }
    });
}

var ksn = typeof ck_ki !== 'undefined' ? ck_ki.keySn : '';

// Error codes (same as original)
var R_FAIL = -1; // 函数执行失败
var R_OK = 0x00000000; // 函数执行成功
var R_NOKEY = 0x00000001; // 没有检测到电子钥匙盘
var R_NOUNIT = 0x00000002; // 没有进行正确初始化的电子钥匙盘
var R_NODRIVER = 0x00000003; // 没有找到电子钥匙盘的驱动文件
var R_KEYLONG = 0x00000004; // 电脑上插入的电子钥匙盘太多
var R_NOUSERKEY = 0x00000005; // 没有插入用户的电子钥匙盘
var R_NOSOKEY = 0x00000006; // 没有插入管理员电子钥匙盘
var R_ERRORPIN = 0x00000007; // 错误的PIN码
var R_NOTLAW = 0x00000008; // 非法的钥匙盘
var R_NOSIGNIMG = 0x00000009; // 没有签章图片可以制作
var R_SAVEERROR = 0x0000000A; // 写入签章图片数据失败
var R_NOSIGN = 0x0000000B; // 钥匙盘里不存在签章
var R_LOADERROR = 0x0000000C; // 读取签章图片数据失败
var R_DELERROR = 0x0000000D; // 删除签章图片数据失败
var R_NOSAMESIGN = 0x0000000E; // 钥匙盘里不存在该签章
var R_NOSPACE = 0x0000000F; // 钥匙盘剩余空间不足
var R_MAXUSER = 0x00000010; // 已达到最大用户量

var R_CANCLEINPUT = 0x00000011; /* 取消输入密码  取消輸入密碼*/
var R_DOWNLOADSIGNET = 0x00000012; /* 下载签章数据失败 下載簽章數據失敗*/ 
var R_ADD_REPETITION = 0x00000013; /* 重复添加印章数据 重複添加印章數據*/
var R_ADMINCERT = 0x00000014; /* 管理员密钥盘无证书 管理員密鑰盤無證書*/
var R_USERCERT = 0x00000015; /* 用户密钥盘无证书 用戶密鑰盤無證書*/

function execute(result) {
    return R_OK == result;
}

function getSMObj() {
    return document.getElementById("SMObj");
}

function getKGHARDEXT() {
    return document.getElementById("KG_HARD_EXT");
}

// Add Linux-specific functions
function getLinuxSMObj() {
    // For Linux, we may need to handle the plugin differently
    var obj = getSMObj();
    if (!obj) {
        console.warn("SMObj not found. This may be a Linux environment without the required plugin.");
        // Try to create a mock object for testing
        return createMockSMObj();
    }
    return obj;
}

// Mock object for testing when plugin is not available
function createMockSMObj() {
    console.warn("Creating mock SMObj for testing purposes");
    return {
        WebGetLocalIP: function() { return "127.0.0.1"; },
        WebGetKeySN: function() { return "MOCK-SN-000001"; },
        WebGetKeyCount: function() { return 1; },
        WebOpenKey: function(index) { return "MOCK-KEY-HANDLE"; },
        WebGetKeySerialNumber: function(handle) { return "MOCK-SN-000001"; },
        WebKeyIsAdmin: function(handle) { return true; },
        WebGetKeyUnit: function(handle) { return "Mock Unit"; },
        WebCloseKey: function(handle) { return true; },
        WebLogin: function(pwd) { return R_OK; },
        WebGetCertData: function(handle, index) { return "MOCK-CERT-DATA"; },
        WebVerifyPin: function(handle, pwd) { return 1; }, // 1 for success in non-IE
        WebSign: function(handle, data) { return "MOCK-SIGNATURE-DATA"; },
        WebDelSign: function(signSn) { return R_OK; },
        WebSaveSign: function(userName, signName, sealData) { return R_OK; },
        WebDelSignWithPwd: function(signSn) { return R_OK; },
        WebDownLoadSealDataWithPwd: function(sealData, userName, signName, width, height) { return 0; },
        WebGetLastError: function() { return R_OK; },
        WebGetLastErrorMsg: function() { return "Success"; },
        WebEditPass: function(oldPwd, newPwd, userAdminFlag) { return true; },
        WebUpdateCert: function(signData) { return "MOCK-UPDATED-SEAL"; }
    };
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        isIE: isIE,
        isLinux: isLinux,
        getSMObj: getSMObj,
        getKGHARDEXT: getKGHARDEXT,
        getLinuxSMObj: getLinuxSMObj
    };
}